import * as vscode from "vscode"
import { QaxNextEditService } from "./QaxNextEditService"
import { DEFAULT_QAX_NEXT_EDIT_CONFIG, QaxNextEditConfig } from "./types/QaxNextEditTypes"
import { AutocompleteConfigManager } from "../AutocompleteConfigManager"

/**
 * QaxNextEdit 提供者，管理服务的注册和配置
 */
export function registerQaxNextEdit(context: vscode.ExtensionContext): void {
	let qaxNextEditService: QaxNextEditService | null = null
	let isCurrentlyEnabled = false
	let commandsRegistered = false

	// 检查配置并更新服务
	const checkAndUpdateService = () => {
		try {
			const config = vscode.workspace.getConfiguration("qax-code.nextEdit")
			const useQaxNextEdit = config.get<boolean>("useQaxNextEdit", false)
			const autocompleteConfigManager = AutocompleteConfigManager.instance
			const isAutocompleteEnabled = autocompleteConfigManager.isEnabled()
			const hasApiKey = autocompleteConfigManager.hasApiKey()
			
			// 只有在启用 QaxNextEdit 且自动完成配置正确时才启用
			const shouldBeEnabled = useQaxNextEdit && isAutocompleteEnabled && hasApiKey

			// 只有状态改变时才采取行动
			if (shouldBeEnabled !== isCurrentlyEnabled) {
				if (qaxNextEditService) {
					qaxNextEditService.dispose()
					qaxNextEditService = null
				}

				if (shouldBeEnabled) {
					qaxNextEditService = setupQaxNextEdit(context, !commandsRegistered)
					commandsRegistered = true
				}

				isCurrentlyEnabled = shouldBeEnabled
			}
		} catch (error) {
			console.error("🚀❌ QaxNextEditProvider error:", error)
		}
	}

	// 初始检查
	checkAndUpdateService()

	// 监听配置变更
	const configWatcher = vscode.workspace.onDidChangeConfiguration((event) => {
		if (
			event.affectsConfiguration("qax-code.nextEdit") ||
			event.affectsConfiguration("qax-code.autocomplete")
		) {
			checkAndUpdateService()
		}
	})

	// 清理资源
	context.subscriptions.push(configWatcher, {
		dispose: () => {
			if (qaxNextEditService) {
				qaxNextEditService.dispose()
				qaxNextEditService = null
			}
		}
	})
}

/**
 * 设置 QaxNextEdit 服务
 */
function setupQaxNextEdit(context: vscode.ExtensionContext, registerCommands: boolean = true): QaxNextEditService {
	// 获取配置
	const config = getQaxNextEditConfig()
	
	// 强制重新创建服务实例
	QaxNextEditService.dispose()
	const qaxNextEditService = QaxNextEditService.getInstance(config)
	
	console.log("🚀🔍 QaxNextEdit: Service initialized with config:", config)

	// 注册命令（只注册一次）
	if (registerCommands) {
		const commands = [
			vscode.commands.registerCommand("qaxNextEdit.toggle", () => {
				const currentState = qaxNextEditService.getState().isEnabled
				qaxNextEditService.setEnabled(!currentState)
				vscode.window.showInformationMessage(`QaxNextEdit ${!currentState ? "enabled" : "disabled"}`)
			}),

			vscode.commands.registerCommand("qaxNextEdit.showSuggestions", async () => {
				const activeEditor = vscode.window.activeTextEditor
				if (!activeEditor) {
					vscode.window.showWarningMessage("No active editor")
					return
				}

				const filePath = activeEditor.document.uri.fsPath
				const suggestions = qaxNextEditService.getJumpSuggestions(filePath)
				
				if (suggestions.length === 0) {
					vscode.window.showInformationMessage("No suggestions available for this file")
					return
				}

				// 显示建议选择器
				const items = suggestions.map(suggestion => ({
					label: suggestion.description,
					description: `${suggestion.filePath}:${suggestion.range.start.line + 1}`,
					detail: suggestion.suggestedEdit?.description,
					suggestion
				}))

				const selected = await vscode.window.showQuickPick(items, {
					placeHolder: "Select a suggestion to apply"
				})

				if (selected) {
					await qaxNextEditService.applyJumpSuggestion(selected.suggestion)
				}
			}),

			vscode.commands.registerCommand("qaxNextEdit.applyAllSuggestions", async () => {
				const activeEditor = vscode.window.activeTextEditor
				if (!activeEditor) {
					vscode.window.showWarningMessage("No active editor")
					return
				}

				const filePath = activeEditor.document.uri.fsPath
				const suggestions = qaxNextEditService.getJumpSuggestions(filePath)
				
				if (suggestions.length === 0) {
					vscode.window.showInformationMessage("No suggestions available for this file")
					return
				}

				const result = await vscode.window.showWarningMessage(
					`Apply all ${suggestions.length} suggestions?`,
					"Yes", "No"
				)

				if (result === "Yes") {
					let appliedCount = 0
					for (const suggestion of suggestions) {
						const success = await qaxNextEditService.applyJumpSuggestion(suggestion)
						if (success) {
							appliedCount++
						}
					}
					vscode.window.showInformationMessage(`Applied ${appliedCount}/${suggestions.length} suggestions`)
				}
			}),

			vscode.commands.registerCommand("qaxNextEdit.clearSuggestions", () => {
				const activeEditor = vscode.window.activeTextEditor
				if (!activeEditor) {
					vscode.window.showWarningMessage("No active editor")
					return
				}

				const filePath = activeEditor.document.uri.fsPath
				const suggestions = qaxNextEditService.getJumpSuggestions(filePath)
				
				for (const suggestion of suggestions) {
					qaxNextEditService.ignoreJumpSuggestion(suggestion)
				}

				vscode.window.showInformationMessage("All suggestions cleared")
			}),

			vscode.commands.registerCommand("qaxNextEdit.showStatus", () => {
				const state = qaxNextEditService.getState()
				const config = qaxNextEditService.getConfig()

				const statusMessage = [
					`QaxNextEdit Status:`,
					`- Enabled: ${state.isEnabled}`,
					`- Analyzing: ${state.isAnalyzing}`,
					`- Active File: ${state.activeFile || "None"}`,
					`- Pending Changes: ${state.pendingChanges.size}`,
					`- Cached Results: ${state.cachedResults.size}`,
					`- Last Analysis: ${state.lastAnalysisTime?.toLocaleTimeString() || "Never"}`,
					``,
					`Configuration:`,
					`- LSP Integration: ${config.enableLSPIntegration}`,
					`- AST Analysis: ${config.enableASTAnalysis}`,
					`- Debounce Delay: ${config.debounceDelayMs}ms`,
					`- Max Suggestions: ${config.maxSuggestions}`,
					`- Confidence Threshold: ${config.confidenceThreshold}`,
					`- Supported Languages: ${config.supportedLanguages.join(", ")}`
				].join("\n")

				vscode.window.showInformationMessage(statusMessage, { modal: true })
			}),

			vscode.commands.registerCommand("qaxNextEdit.applySuggestion", async (suggestionJson: string) => {
				try {
					const suggestion = JSON.parse(decodeURIComponent(suggestionJson))
					await qaxNextEditService.applyJumpSuggestion(suggestion)
				} catch (error) {
					console.error("Failed to apply suggestion:", error)
					vscode.window.showErrorMessage("Failed to apply suggestion")
				}
			}),

			vscode.commands.registerCommand("qaxNextEdit.ignoreSuggestion", (suggestionJson: string) => {
				try {
					const suggestion = JSON.parse(decodeURIComponent(suggestionJson))
					qaxNextEditService.ignoreJumpSuggestion(suggestion)
				} catch (error) {
					console.error("Failed to ignore suggestion:", error)
					vscode.window.showErrorMessage("Failed to ignore suggestion")
				}
			}),

			vscode.commands.registerCommand("qaxNextEdit.nextSuggestion", () => {
				qaxNextEditService.navigateToNextSuggestion()
			}),

			vscode.commands.registerCommand("qaxNextEdit.previousSuggestion", () => {
				qaxNextEditService.navigateToPreviousSuggestion()
			})
		]

		// 添加命令到上下文订阅
		context.subscriptions.push(...commands)
	}

	return qaxNextEditService
}

/**
 * 获取 QaxNextEdit 配置
 */
function getQaxNextEditConfig(): QaxNextEditConfig {
	const config = vscode.workspace.getConfiguration("qax-code.nextEdit")
	
	return {
		enabled: config.get<boolean>("enabled", DEFAULT_QAX_NEXT_EDIT_CONFIG.enabled),
		enableLSPIntegration: config.get<boolean>("enableLSPIntegration", DEFAULT_QAX_NEXT_EDIT_CONFIG.enableLSPIntegration),
		enableASTAnalysis: config.get<boolean>("enableASTAnalysis", DEFAULT_QAX_NEXT_EDIT_CONFIG.enableASTAnalysis),
		debounceDelayMs: config.get<number>("debounceDelayMs", DEFAULT_QAX_NEXT_EDIT_CONFIG.debounceDelayMs),
		maxSuggestions: config.get<number>("maxSuggestions", DEFAULT_QAX_NEXT_EDIT_CONFIG.maxSuggestions),
		confidenceThreshold: config.get<number>("confidenceThreshold", DEFAULT_QAX_NEXT_EDIT_CONFIG.confidenceThreshold),
		supportedLanguages: config.get<string[]>("supportedLanguages", DEFAULT_QAX_NEXT_EDIT_CONFIG.supportedLanguages),
		analysisDepth: config.get<"shallow" | "deep">("analysisDepth", DEFAULT_QAX_NEXT_EDIT_CONFIG.analysisDepth)
	}
}

/**
 * 更新 QaxNextEdit 配置
 */
export async function updateQaxNextEditConfig(updates: Partial<QaxNextEditConfig>): Promise<void> {
	const config = vscode.workspace.getConfiguration("qax-code.nextEdit")
	
	for (const [key, value] of Object.entries(updates)) {
		await config.update(key, value, vscode.ConfigurationTarget.Global)
	}
}
