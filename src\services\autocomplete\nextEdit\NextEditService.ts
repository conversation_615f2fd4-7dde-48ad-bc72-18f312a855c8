import * as vscode from "vscode"
import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "../../../api"
import { AutocompleteTaskManager } from "../AutocompleteTaskManager"
import { NextEditContextCollector } from "./NextEditContextCollector"
import { NextEditRecommendationEngine } from "./NextEditRecommendationEngine"
import { NextEditUIProvider } from "./NextEditUIProvider"
import {
	NextEditConfig,
	NextEditFileState,
	NextEditSuggestion,
	NextEditEvent,
	NextEditEventType,
	DEFAULT_NEXT_EDIT_CONFIG,
} from "./types/NextEditTypes"
import { createDebouncedFn } from "../utils/createDebouncedFn"

/**
 * Main service for Next Edit functionality
 */
export class NextEditService {
	private static instance: NextEditService | null = null

	private config: NextEditConfig
	private contextCollector: NextEditContextCollector
	private recommendationEngine: NextEditRecommendationEngine
	private uiProvider: NextEditUIProvider
	private fileStates: Map<string, NextEditFileState> = new Map()
	private disposables: vscode.Disposable[] = []
	private isEnabled: boolean = true
	private debouncedRequestSuggestions: (filePath: string, document: vscode.TextDocument) => void

	private constructor(config: NextEditConfig = DEFAULT_NEXT_EDIT_CONFIG, apiHandler: ApiHandler | null = null) {
		this.config = config
		this.contextCollector = new NextEditContextCollector(config)
		this.recommendationEngine = new NextEditRecommendationEngine(apiHandler || undefined)
		this.uiProvider = new NextEditUIProvider()

		// Create debounced function for requesting suggestions
		this.debouncedRequestSuggestions = createDebouncedFn(this.requestSuggestions.bind(this), config.debounceDelayMs)

		this.setupEventHandlers()
		this.setupUIEventHandlers()
	}

	/**
	 * Get singleton instance
	 */
	static getInstance(config?: NextEditConfig, apiHandler?: ApiHandler | null): NextEditService {
		if (!NextEditService.instance) {
			NextEditService.instance = new NextEditService(config, apiHandler)
		}
		return NextEditService.instance
	}

	/**
	 * Dispose singleton instance
	 */
	static dispose(): void {
		if (NextEditService.instance) {
			NextEditService.instance.dispose()
			NextEditService.instance = null
		}
	}

	/**
	 * Initialize the service
	 */
	initialize(apiHandler: ApiHandler | null): void {
		this.recommendationEngine.updateApiHandler(apiHandler)
	}

	/**
	 * Setup event handlers for file changes and editor events
	 */
	private setupEventHandlers(): void {
		// Listen for document changes
		this.disposables.push(
			vscode.workspace.onDidChangeTextDocument((event) => {
				if (this.shouldProcessDocument(event.document)) {
					this.onDocumentChanged(event.document)
				}
			}),
		)

		// Listen for active editor changes
		this.disposables.push(
			vscode.window.onDidChangeActiveTextEditor((editor) => {
				if (editor && this.shouldProcessDocument(editor.document)) {
					this.onActiveEditorChanged(editor)
				}
			}),
		)

		// Listen for document open/close
		this.disposables.push(
			vscode.workspace.onDidOpenTextDocument((document) => {
				if (this.shouldProcessDocument(document)) {
					this.onDocumentOpened(document)
				}
			}),
		)

		this.disposables.push(
			vscode.workspace.onDidCloseTextDocument((document) => {
				// Note: We don't remove suggestions when document is closed
				// as per requirement to keep suggestions until file is modified
			}),
		)
	}

	/**
	 * Setup UI event handlers
	 */
	private setupUIEventHandlers(): void {
		this.uiProvider.setEventCallback((event: NextEditEvent) => {
			this.handleUIEvent(event)
		})

		// Commands are now registered in NextEditProvider to avoid duplication
	}

	/**
	 * Check if document should be processed
	 */
	private shouldProcessDocument(document: vscode.TextDocument): boolean {
		// Skip if service is disabled
		if (!this.isEnabled || !this.config.enabled) {
			return false
		}

		// Skip if autocomplete is temporarily disabled
		const taskManager = AutocompleteTaskManager.getInstance()
		if (taskManager.isTemporarilyDisabled()) {
			return false
		}

		// Skip certain file types
		const scheme = document.uri.scheme
		if (scheme !== "file" && scheme !== "untitled") {
			return false
		}

		// Skip very small files
		if (document.lineCount <= 2) {
			return false
		}

		return true
	}

	/**
	 * Handle document changes
	 */
	private onDocumentChanged(document: vscode.TextDocument): void {
		const filePath = document.uri.fsPath

		// Clear existing suggestions for this file
		this.clearSuggestionsForFile(filePath)

		// Request new suggestions (debounced)
		this.debouncedRequestSuggestions(filePath, document)
	}

	/**
	 * Handle active editor changes
	 */
	private onActiveEditorChanged(editor: vscode.TextEditor): void {
		const filePath = editor.document.uri.fsPath
		const fileState = this.fileStates.get(filePath)

		if (fileState && fileState.suggestions.length > 0) {
			// Show existing suggestions for this file
			this.showSuggestionsForFile(filePath)
		}
	}

	/**
	 * Handle document opened
	 */
	private onDocumentOpened(document: vscode.TextDocument): void {
		const filePath = document.uri.fsPath
		const fileState = this.fileStates.get(filePath)

		if (fileState && fileState.suggestions.length > 0) {
			// Show existing suggestions for this file
			this.showSuggestionsForFile(filePath)
		}
	}

	/**
	 * Request suggestions for a file
	 */
	private async requestSuggestions(filePath: string, document: vscode.TextDocument): Promise<void> {
		try {
			console.log("🚀🔍 NextEdit: Requesting suggestions for", filePath)

			// Get current cursor position from active editor
			const editor = vscode.window.activeTextEditor
			const position = editor && editor.document.uri.fsPath === filePath ? editor.selection.active : undefined

			// Collect context with cursor position
			const context = await this.contextCollector.collectContext(document, position)

			// Generate suggestions
			const suggestions = await this.recommendationEngine.generateSuggestions(context)

			if (suggestions.length > 0) {
				// Limit to max suggestions
				const limitedSuggestions = suggestions.slice(0, this.config.maxSuggestions)

				// Store suggestions
				this.fileStates.set(filePath, {
					filePath,
					suggestions: limitedSuggestions,
					currentSuggestionIndex: 0,
					isShowingSuggestions: false,
					lastModified: new Date(),
				})

				// Show suggestions if this is the active file
				const activeEditor = vscode.window.activeTextEditor
				if (activeEditor && activeEditor.document.uri.fsPath === filePath) {
					this.showSuggestionsForFile(filePath)
				}

				console.log(`🚀✨ NextEdit: Stored ${limitedSuggestions.length} suggestions for ${filePath}`)
			}
		} catch (error) {
			console.error("NextEditService: Failed to request suggestions:", error)
		}
	}

	/**
	 * Show suggestions for a file
	 */
	private async showSuggestionsForFile(filePath: string): Promise<void> {
		const fileState = this.fileStates.get(filePath)
		if (!fileState || fileState.suggestions.length === 0) {
			return
		}

		fileState.isShowingSuggestions = true

		// Use the new showSuggestions method that handles multiple suggestions
		await this.uiProvider.showSuggestions(fileState.suggestions)
	}

	/**
	 * Clear suggestions for a file
	 */
	private clearSuggestionsForFile(filePath: string): void {
		const fileState = this.fileStates.get(filePath)
		if (fileState) {
			fileState.suggestions = []
			fileState.currentSuggestionIndex = 0
			fileState.isShowingSuggestions = false
		}

		this.uiProvider.clearUI()
	}

	/**
	 * Navigate to next suggestion
	 */
	public async navigateToNextSuggestion(): Promise<void> {
		const activeEditor = vscode.window.activeTextEditor
		if (!activeEditor) {
			return
		}

		const filePath = activeEditor.document.uri.fsPath
		const fileState = this.fileStates.get(filePath)
		if (!fileState || fileState.suggestions.length === 0) {
			return
		}

		fileState.currentSuggestionIndex = (fileState.currentSuggestionIndex + 1) % fileState.suggestions.length
		await this.showSuggestionsForFile(filePath)
	}

	/**
	 * Navigate to previous suggestion
	 */
	public async navigateToPreviousSuggestion(): Promise<void> {
		const activeEditor = vscode.window.activeTextEditor
		if (!activeEditor) {
			return
		}

		const filePath = activeEditor.document.uri.fsPath
		const fileState = this.fileStates.get(filePath)
		if (!fileState || fileState.suggestions.length === 0) {
			return
		}

		fileState.currentSuggestionIndex =
			fileState.currentSuggestionIndex === 0 ? fileState.suggestions.length - 1 : fileState.currentSuggestionIndex - 1
		await this.showSuggestionsForFile(filePath)
	}

	/**
	 * Handle UI events
	 */
	private handleUIEvent(event: NextEditEvent): void {
		if (event.type === NextEditEventType.SUGGESTION_APPLIED || event.type === NextEditEventType.SUGGESTION_IGNORED) {
			// Move to next suggestion or clear if no more
			const filePath = event.filePath
			if (filePath) {
				const fileState = this.fileStates.get(filePath)
				if (fileState) {
					// Remove the current suggestion
					fileState.suggestions.splice(fileState.currentSuggestionIndex, 1)

					if (fileState.suggestions.length === 0) {
						// No more suggestions
						this.clearSuggestionsForFile(filePath)
					} else {
						// Adjust index if needed
						if (fileState.currentSuggestionIndex >= fileState.suggestions.length) {
							fileState.currentSuggestionIndex = 0
						}
						// Show next suggestion
						this.showSuggestionsForFile(filePath)
					}
				}
			}
		}
	}

	/**
	 * Enable/disable the service
	 */
	setEnabled(enabled: boolean): void {
		this.isEnabled = enabled
		if (!enabled) {
			// Clear all UI when disabled
			this.uiProvider.clearUI()
		}
		console.log(`🚀🔍 NextEditService ${enabled ? "enabled" : "disabled"}`)
	}

	/**
	 * Update configuration
	 */
	updateConfig(config: Partial<NextEditConfig>): void {
		this.config = { ...this.config, ...config }
		this.contextCollector.updateConfig(this.config)

		// Recreate debounced function if delay changed
		if (config.debounceDelayMs) {
			this.debouncedRequestSuggestions = createDebouncedFn(this.requestSuggestions.bind(this), config.debounceDelayMs)
		}
	}

	/**
	 * Update API handler
	 */
	updateApiHandler(apiHandler: ApiHandler | null): void {
		this.recommendationEngine.updateApiHandler(apiHandler)
	}

	/**
	 * Dispose the service
	 */
	dispose(): void {
		this.disposables.forEach((disposable) => disposable.dispose())
		this.disposables = []
		this.uiProvider.dispose()
		this.fileStates.clear()
		NextEditService.instance = null
		console.log("🚀🔍 NextEditService disposed")
	}
}
